version: '3.8'

services:
  ai-agent-general:
    build: .
    container_name: ai-agent-general
    network_mode: "host"  # To access localhost:1234 (local LLM)
    volumes:
      - ./config.json:/root/config.json
      - ./reports:/root/reports
    restart: unless-stopped

  # Optional: PostgreSQL for persistence
  postgres:
    image: postgres:15-alpine
    container_name: agent-db
    environment:
      POSTGRES_DB: agent
      POSTGRES_USER: agent
      POSTGRES_PASSWORD: agent_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data:

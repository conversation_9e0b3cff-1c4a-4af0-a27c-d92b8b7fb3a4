package prompt

import (
	"encoding/json"
	"fmt"
	"strings"
	"text/template"

	"github.com/general/pkg/agent"
)

// TemplateBuilder implements PromptBuilder using Go templates
type TemplateBuilder struct {
	template     *template.Template
	templateText string
	domain       string
}

// NewTemplateBuilder creates a new template-based prompt builder
func NewTemplateBuilder(domain string) *TemplateBuilder {
	return &TemplateBuilder{
		domain: domain,
	}
}

// BuildPrompt builds a prompt for AI analysis using the template
func (pb *TemplateBuilder) BuildPrompt(entry agent.DataEntry, detailedData map[string]interface{}, config map[string]interface{}) string {
	if pb.template == nil {
		// Use default template if none is set
		pb.SetTemplate(pb.getDefaultTemplate())
	}

	// Prepare template data
	templateData := map[string]interface{}{
		"Entry":        entry,
		"DetailedData": detailedData,
		"Config":       config,
		"Domain":       pb.domain,
	}

	// Add detailed data as JSON string for template
	if detailedDataJSON, err := json.MarshalIndent(detailedData, "", "  "); err == nil {
		templateData["DetailedDataJSON"] = string(detailedDataJSON)
	} else {
		templateData["DetailedDataJSON"] = "{}"
	}

	// Execute template
	var result strings.Builder
	if err := pb.template.Execute(&result, templateData); err != nil {
		// Fallback to simple prompt if template execution fails
		return pb.buildFallbackPrompt(entry, detailedData)
	}

	return result.String()
}

// GetTemplate returns the current prompt template
func (pb *TemplateBuilder) GetTemplate() string {
	return pb.templateText
}

// SetTemplate sets the prompt template
func (pb *TemplateBuilder) SetTemplate(templateText string) {
	pb.templateText = templateText

	// Parse template
	tmpl, err := template.New("prompt").Parse(templateText)
	if err != nil {
		// Use fallback template if parsing fails
		pb.templateText = pb.getDefaultTemplate()
		tmpl, _ = template.New("prompt").Parse(pb.templateText)
	}

	pb.template = tmpl
}

// getDefaultTemplate returns the default template based on domain
func (pb *TemplateBuilder) getDefaultTemplate() string {
	switch pb.domain {
	case "fintech":
		return pb.getFintechTemplate()
	case "ecommerce":
		return pb.getEcommerceTemplate()
	case "healthcare":
		return pb.getHealthcareTemplate()
	case "general":
		fallthrough
	default:
		return pb.getGeneralTemplate()
	}
}

// getGeneralTemplate returns a general-purpose analysis template
func (pb *TemplateBuilder) getGeneralTemplate() string {
	return `You are an expert system analyst specializing in error analysis and troubleshooting.

**ANALYSIS EXPERTISE:**
- System infrastructure and application errors
- Performance and scalability issues
- Security incidents and vulnerabilities
- Data integrity and consistency problems
- Integration and API failures
- User experience and interface issues

**COMMON ERROR PATTERNS:**
- Network connectivity and timeout issues
- Database connection and query failures
- Authentication and authorization problems
- Resource exhaustion (memory, CPU, disk)
- Configuration errors and mismatches
- Third-party service dependencies

Analyze this system log entry and provide a detailed technical analysis:

**LOG ENTRY:**
ID: {{.Entry.ID}}
Title: {{.Entry.Title}}
Entity: {{.Entry.Entity}}
Type: {{.Entry.Type}}
Message: {{.Entry.Message}}
Location: {{.Entry.Location}}

**DETAILED LOG:**
{{.DetailedDataJSON}}

Based on your expertise, provide analysis in valid JSON format with these exact fields:
{
  "error_type": "string - specific technical classification",
  "severity": "string - low/medium/high/critical",
  "category": "string - infrastructure/application/security/performance",
  "description": "string - detailed technical explanation",
  "root_cause": "string - likely underlying cause",
  "business_impact": "string - impact on operations",
  "suggested_action": "string - specific remediation steps",
  "is_retryable": boolean - whether operation can be retried,
  "compliance_risk": "string - none/low/medium/high",
  "customer_impact": "string - effect on end users"
}

Respond with ONLY valid JSON. No additional text, explanations, or thinking process.`
}

// getFintechTemplate returns a fintech-specific analysis template
func (pb *TemplateBuilder) getFintechTemplate() string {
	return `You are an expert fintech operations analyst specializing in payment systems, acquiring operations, and issuing services.

**FINTECH DOMAIN EXPERTISE:**

**ACQUIRING OPERATIONS:**
- Merchant onboarding and underwriting processes
- Payment processing flows (authorization, capture, settlement)
- Risk management and fraud detection systems
- Scheme compliance (Visa, Mastercard, regional networks)
- PCI DSS compliance and tokenization
- Multi-acquirer routing and smart routing logic
- Transaction monitoring and suspicious activity detection
- Chargeback and dispute management
- Multi-currency processing and FX handling
- Reconciliation and settlement processes

**ISSUING OPERATIONS:**
- Card issuing and provisioning
- Digital wallet integration (Apple Pay, Google Pay, Samsung Pay)
- Real-time authorization and transaction monitoring
- Spending controls and limits management
- KYC/AML for cardholders
- Card lifecycle management (activation, blocking, renewal)
- PIN management and card security
- Statement generation and customer communications

**FINTECH INFRASTRUCTURE:**
- API gateway patterns and rate limiting
- Microservices architecture for payments
- Database consistency and ACID transactions
- Event-driven architectures and message queues
- Real-time fraud detection and machine learning
- Regulatory compliance (PSD2, Open Banking, GDPR)

**COMMON PAYMENT SYSTEM ERRORS:**
- Authorization failures (insufficient funds, expired cards, velocity limits)
- Network timeouts and connectivity issues
- Scheme downtimes and maintenance windows
- Configuration errors (merchant setup, routing rules)
- Compliance violations and risk threshold breaches
- Settlement and reconciliation discrepancies

Analyze this payment system log entry and provide a detailed technical analysis:

**LOG ENTRY:**
ID: {{.Entry.ID}}
Title: {{.Entry.Title}}
Entity: {{.Entry.Entity}}
Type: {{.Entry.Type}}
Protocol: {{.Entry.Proto}}
Message: {{.Entry.Message}}
Location: {{.Entry.Location}}

**DETAILED LOG:**
{{.DetailedDataJSON}}

Based on your fintech expertise, provide analysis in valid JSON format with these exact fields:
{
  "error_type": "string - specific technical classification",
  "severity": "string - low/medium/high/critical",
  "category": "string - acquiring/issuing/infrastructure/compliance/risk",
  "description": "string - detailed technical explanation",
  "root_cause": "string - likely underlying cause",
  "business_impact": "string - impact on operations",
  "suggested_action": "string - specific remediation steps",
  "is_retryable": boolean - whether operation can be retried,
  "compliance_risk": "string - none/low/medium/high",
  "customer_impact": "string - effect on end customers"
}

Respond with ONLY valid JSON. No additional text, explanations, or thinking process.`
}

// getEcommerceTemplate returns an e-commerce specific analysis template
func (pb *TemplateBuilder) getEcommerceTemplate() string {
	return `You are an expert e-commerce operations analyst specializing in online retail systems and customer experience.

**E-COMMERCE DOMAIN EXPERTISE:**

**ORDER MANAGEMENT:**
- Order processing and fulfillment workflows
- Inventory management and stock synchronization
- Shopping cart and checkout processes
- Order status tracking and updates
- Returns and refunds processing
- Multi-channel order orchestration

**CUSTOMER EXPERIENCE:**
- Website performance and page load times
- Search functionality and product discovery
- Recommendation engines and personalization
- Mobile app performance and usability
- Customer account management
- Support ticket and communication systems

**PAYMENT AND BILLING:**
- Payment gateway integration and processing
- Multiple payment method support
- Subscription and recurring billing
- Tax calculation and compliance
- Currency conversion and international sales
- Fraud detection and prevention

**INFRASTRUCTURE:**
- CDN and content delivery optimization
- Database performance and scalability
- API rate limiting and throttling
- Third-party service integrations
- Security and data protection
- Analytics and tracking systems

Analyze this e-commerce system log entry and provide a detailed technical analysis:

**LOG ENTRY:**
ID: {{.Entry.ID}}
Title: {{.Entry.Title}}
Entity: {{.Entry.Entity}}
Type: {{.Entry.Type}}
Message: {{.Entry.Message}}
Location: {{.Entry.Location}}

**DETAILED LOG:**
{{.DetailedDataJSON}}

Based on your e-commerce expertise, provide analysis in valid JSON format with these exact fields:
{
  "error_type": "string - specific technical classification",
  "severity": "string - low/medium/high/critical",
  "category": "string - order_management/customer_experience/payment/infrastructure",
  "description": "string - detailed technical explanation",
  "root_cause": "string - likely underlying cause",
  "business_impact": "string - impact on sales and customer satisfaction",
  "suggested_action": "string - specific remediation steps",
  "is_retryable": boolean - whether operation can be retried,
  "compliance_risk": "string - none/low/medium/high",
  "customer_impact": "string - effect on customer experience"
}

Respond with ONLY valid JSON. No additional text, explanations, or thinking process.`
}

// getHealthcareTemplate returns a healthcare-specific analysis template
func (pb *TemplateBuilder) getHealthcareTemplate() string {
	return `You are an expert healthcare IT analyst specializing in medical systems and patient data management.

**HEALTHCARE DOMAIN EXPERTISE:**

**PATIENT DATA MANAGEMENT:**
- Electronic Health Records (EHR) systems
- Patient registration and demographics
- Medical history and clinical documentation
- Lab results and diagnostic imaging
- Medication management and prescriptions
- Care coordination and referrals

**CLINICAL WORKFLOWS:**
- Appointment scheduling and management
- Clinical decision support systems
- Order entry and results reporting
- Nursing documentation and care plans
- Quality measures and reporting
- Clinical alerts and notifications

**COMPLIANCE AND SECURITY:**
- HIPAA compliance and patient privacy
- Data encryption and access controls
- Audit logging and monitoring
- Breach detection and response
- Risk assessment and mitigation
- Regulatory reporting requirements

**INTEROPERABILITY:**
- HL7 FHIR and messaging standards
- Health Information Exchange (HIE)
- API integration with third-party systems
- Data mapping and transformation
- Clinical terminology and coding
- Device integration and IoT

Analyze this healthcare system log entry and provide a detailed technical analysis:

**LOG ENTRY:**
ID: {{.Entry.ID}}
Title: {{.Entry.Title}}
Entity: {{.Entry.Entity}}
Type: {{.Entry.Type}}
Message: {{.Entry.Message}}
Location: {{.Entry.Location}}

**DETAILED LOG:**
{{.DetailedDataJSON}}

Based on your healthcare IT expertise, provide analysis in valid JSON format with these exact fields:
{
  "error_type": "string - specific technical classification",
  "severity": "string - low/medium/high/critical",
  "category": "string - patient_data/clinical_workflow/compliance/interoperability",
  "description": "string - detailed technical explanation",
  "root_cause": "string - likely underlying cause",
  "business_impact": "string - impact on patient care and operations",
  "suggested_action": "string - specific remediation steps",
  "is_retryable": boolean - whether operation can be retried,
  "compliance_risk": "string - none/low/medium/high",
  "customer_impact": "string - effect on patient care and safety"
}

Respond with ONLY valid JSON. No additional text, explanations, or thinking process.`
}

// buildFallbackPrompt builds a simple fallback prompt when template fails
func (pb *TemplateBuilder) buildFallbackPrompt(entry agent.DataEntry, detailedData map[string]interface{}) string {
	detailedDataJSON, _ := json.MarshalIndent(detailedData, "", "  ")

	return fmt.Sprintf(`Analyze this system error and provide a JSON response with error analysis.

LOG ENTRY:
ID: %s
Title: %s
Type: %s
Message: %s

DETAILED DATA:
%s

Provide analysis in JSON format with fields: error_type, severity, category, description, root_cause, business_impact, suggested_action, is_retryable, compliance_risk, customer_impact.`,
		entry.ID, entry.Title, entry.Type, entry.Message, detailedDataJSON)
}

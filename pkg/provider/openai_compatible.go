package provider

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/general/pkg/agent"
	"github.com/general/pkg/defaults"
	"github.com/go-resty/resty/v2"
)

type OpenAICompatibleProvider struct {
	Client      *resty.Client
	ApiURL      string
	ModelName   string
	Temperature float64
	MaxTokens   int
	ApiKey      string
}

type OpenAIRequest struct {
	Model       string              `json:"model"`
	Messages    []map[string]string `json:"messages"`
	MaxTokens   int                 `json:"max_tokens"`
	Temperature float64             `json:"temperature"`
}

type OpenAIResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Error *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
	} `json:"error,omitempty"`
}

func NewOpenAICompatibleProvider() *OpenAICompatibleProvider {
	return &OpenAICompatibleProvider{}
}

func (p *OpenAICompatibleProvider) Initialize(config map[string]interface{}) error {
	api_url, ok := config["api_url"].(string)
	if !ok || api_url == "" {
		return fmt.Errorf("api_url is required")
	}
	p.ApiURL = api_url

	model_name, ok := config["model_name"].(string)
	if !ok || model_name == "" {
		return fmt.Errorf("model_name is required")
	}
	p.ModelName = model_name

	if temp, ok := config["temperature"].(float64); ok {
		p.Temperature = temp
	} else {
		p.Temperature = defaults.DEFAULT_TEMPERATURE
	}

	if max_tokens, ok := config["max_tokens"].(int); ok {
		p.MaxTokens = max_tokens
	} else {
		p.MaxTokens = defaults.DEFAULT_MAXTOKENS
	}

	if api_key, ok := config["api_key"].(string); ok {
		p.ApiKey = api_key
	}

	p.Client = resty.New()
	if timeoutSeconds, ok := config["timeout_seconds"].(int); ok {
		p.Client.SetTimeout(time.Duration(timeoutSeconds) * time.Second)
	} else {
		p.Client.SetTimeout(defaults.DEFAULT_TIMEOUT * time.Second)
	}

	return nil
}

func (p *OpenAICompatibleProvider) Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*agent.Analysis, error) {
	request := OpenAIRequest{
		Model: p.ModelName,
		Messages: []map[string]string{
			{
				"role":    "user",
				"content": prompt,
			},
		},
		MaxTokens:   p.MaxTokens,
		Temperature: p.Temperature,
	}

	req := p.Client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(request)

	if p.ApiKey != "" {
		req.SetHeader("Authorization", "Bearer "+p.ApiKey)
	}

	resp, err := req.Post(p.ApiURL)
	if err != nil {
		return nil, fmt.Errorf("API request failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), resp.String())
	}

	var api_response OpenAIResponse
	if err := json.Unmarshal(resp.Body(), &api_response); err != nil {
		return nil, fmt.Errorf("failed to parse API response: %w", err)
	}

	if api_response.Error != nil {
		return nil, fmt.Errorf("API error: %s", api_response.Error.Message)
	}

	if len(api_response.Choices) == 0 {
		return nil, fmt.Errorf("no choices in API response")
	}

	aiContent := api_response.Choices[0].Message.Content

	analysis, err := p.extractJSONFromResponse(aiContent)
	if err != nil {
		return p.createFallbackAnalysis(fmt.Sprintf("Failed to parse AI response: %v", err)), nil
	}

	return analysis, nil
}

// extractJSONFromResponse extracts JSON from AI response that may contain thinking tags
func (p *OpenAICompatibleProvider) extractJSONFromResponse(text string) (*agent.Analysis, error) {
	// Remove think tags
	thinkPattern := regexp.MustCompile(`(?s)<think>.*?</think>`)
	cleanedText := thinkPattern.ReplaceAllString(text, "")

	// Clean up the text - remove extra whitespace and normalize
	cleanedText = regexp.MustCompile(`\s+`).ReplaceAllString(cleanedText, " ")
	cleanedText = strings.TrimSpace(cleanedText)

	// Find JSON object using bracket matching
	startIdx := strings.Index(cleanedText, "{")
	if startIdx == -1 {
		return nil, fmt.Errorf("no JSON object found")
	}

	bracketCount := 0
	endIdx := startIdx

	for i, char := range cleanedText[startIdx:] {
		if char == '{' {
			bracketCount++
		} else if char == '}' {
			bracketCount--
			if bracketCount == 0 {
				endIdx = startIdx + i + 1
				break
			}
		}
	}

	if bracketCount != 0 {
		return nil, fmt.Errorf("unmatched brackets in JSON")
	}

	jsonStr := cleanedText[startIdx:endIdx]

	// Clean up common JSON formatting issues
	jsonStr = regexp.MustCompile(`\s+`).ReplaceAllString(jsonStr, " ")
	jsonStr = regexp.MustCompile(`,\s*}`).ReplaceAllString(jsonStr, "}")
	jsonStr = regexp.MustCompile(`,\s*]`).ReplaceAllString(jsonStr, "]")

	var analysis agent.Analysis
	if err := json.Unmarshal([]byte(jsonStr), &analysis); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	return &analysis, nil
}

// for a fallback analysis when AI parsing fails
func (p *OpenAICompatibleProvider) createFallbackAnalysis(reason string) *agent.Analysis {
	return &agent.Analysis{
		ErrorType:       "Analysis Parsing Error",
		Severity:        "medium",
		Category:        "infrastructure",
		Description:     fmt.Sprintf("Failed to parse AI analysis response: %s", reason),
		RootCause:       "AI response parsing failure",
		BusinessImpact:  "Analysis quality may be reduced",
		SuggestedAction: "Review AI prompt and response format",
		IsRetryable:     true,
		ComplianceRisk:  "low",
		CustomerImpact:  "No direct customer impact",
		Confidence:      0.5,
		Tags:            []string{"parsing_error", "fallback"},
	}
}

// GetCapabilities returns the capabilities of the AI provider
func (p *OpenAICompatibleProvider) GetCapabilities() map[string]interface{} {
	return map[string]interface{}{
		"type":               "openai_compatible",
		"model":              p.ModelName,
		"max_tokens":         p.MaxTokens,
		"temperature":        p.Temperature,
		"supports_streaming": false,
		"supports_functions": false,
		"api_url":            p.ApiURL,
	}
}

func (p *OpenAICompatibleProvider) Close() error {
	return nil
}

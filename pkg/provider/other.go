package provider

import (
	"context"
	"fmt"
	"time"

	"github.com/general/pkg/agent"
	"github.com/general/pkg/defaults"
	"github.com/go-resty/resty/v2"
)

type OtherProvider struct {
	Client      *resty.Client
	ApiKey      string
	ModelName   string
	MaxTokens   int
	Temperature float64
}

func NewOtherProvider() *OtherProvider {
	return &OtherProvider{}
}

func (p *OtherProvider) Initialize(config map[string]interface{}) error {
	api_key, ok := config["api_key"].(string)
	if !ok || api_key == "" {
		return fmt.Errorf("api_key is required for Other provider")
	}
	p.ApiKey = api_key

	model_name, ok := config["model_name"].(string)
	if !ok || model_name == "" {
		return fmt.Errorf("model_name is required")
	}
	p.ModelName = model_name

	if temp, ok := config["temperature"].(float64); ok {
		p.Temperature = temp
	} else {
		p.Temperature = defaults.DEFAULT_TEMPERATURE
	}

	if max_tokens, ok := config["max_tokens"].(int); ok {
		p.MaxTokens = max_tokens
	} else {
		p.MaxTokens = defaults.DEFAULT_MAXTOKENS
	}

	if api_key, ok := config["api_key"].(string); ok {
		p.ApiKey = api_key
	}

	p.Client = resty.New()
	if timeoutSeconds, ok := config["timeout_seconds"].(int); ok {
		p.Client.SetTimeout(time.Duration(timeoutSeconds) * time.Second)
	} else {
		p.Client.SetTimeout(defaults.DEFAULT_TIMEOUT * time.Second)
	}

	return nil
}

func (p *OtherProvider) Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*agent.Analysis, error) {
	// TODO: Implement Other API call
	return nil, fmt.Errorf("Other provider not implemented yet")
}

func (p *OtherProvider) GetCapabilities() map[string]interface{} {
	return map[string]interface{}{
		"type":               "other",
		"model":              p.ModelName,
		"max_tokens":         p.MaxTokens,
		"temperature":        p.Temperature,
		"supports_streaming": true,
		"supports_functions": false,
	}
}

func (p *OtherProvider) Close() error {
	return nil
}

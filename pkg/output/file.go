package output

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/general/pkg/agent"
)

type FileOutputHandler struct {
	ReportsDirectory string
	FilenamePattern  string
	IncludeRawData   bool
	PrettyPrint      bool
}

func NewFileOutputHandler() *FileOutputHandler {
	return &FileOutputHandler{}
}

func (h *FileOutputHandler) Initialize(config map[string]interface{}) error {
	if reports_dir, ok := config["reports_directory"].(string); ok {
		h.ReportsDirectory = reports_dir
	} else {
		h.ReportsDirectory = "reports/"
	}

	if pattern, ok := config["filename_pattern"].(string); ok {
		h.FilenamePattern = pattern
	} else {
		h.FilenamePattern = "analysis_{id}_{timestamp}.json"
	}

	if include_raw, ok := config["include_raw_data"].(bool); ok {
		h.IncludeRawData = include_raw
	}

	if pretty_print, ok := config["pretty_print"].(bool); ok {
		h.PrettyPrint = pretty_print
	} else {
		h.Pretty<PERSON>rint = true
	}

	// ----->if it doesn't exist
	if err := os.MkdirAll(h.ReportsDirectory, 0755); err != nil {
		return fmt.Errorf("failed to create reports directory: %w", err)
	}

	return nil
}

func (h *FileOutputHandler) SaveReport(ctx context.Context, report agent.Report) error {
	filename := h.generateFilename(report)
	fullPath := filepath.Join(h.ReportsDirectory, filename)

	reportData := h.prepareReportData(report)

	var data []byte
	var err error

	if h.PrettyPrint {
		data, err = json.MarshalIndent(reportData, "", "  ")
	} else {
		data, err = json.Marshal(reportData)
	}

	if err != nil {
		return fmt.Errorf("failed to marshal report: %w", err)
	}

	if err := os.WriteFile(fullPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write report file: %w", err)
	}

	return nil
}

func (h *FileOutputHandler) generateFilename(report agent.Report) string {
	filename := h.FilenamePattern

	filename = strings.ReplaceAll(filename, "{id}", report.DataEntry.ID)
	filename = strings.ReplaceAll(filename, "{timestamp}", fmt.Sprintf("%d", time.Now().Unix()))
	filename = strings.ReplaceAll(filename, "{date}", time.Now().Format("2006-01-02"))
	filename = strings.ReplaceAll(filename, "{time}", time.Now().Format("15-04-05"))
	filename = strings.ReplaceAll(filename, "{severity}", report.Analysis.Severity)
	filename = strings.ReplaceAll(filename, "{category}", report.Analysis.Category)

	if !strings.HasSuffix(filename, ".json") {
		filename += ".json"
	}

	return filename
}

func (h *FileOutputHandler) prepareReportData(report agent.Report) map[string]interface{} {
	data := map[string]interface{}{
		"timestamp":    report.Timestamp,
		"agent_info":   report.AgentInfo,
		"analysis":     report.Analysis,
		"processed_by": report.ProcessedBy,
	}

	if h.IncludeRawData {
		data["data_entry"] = report.DataEntry
	} else {
		data["data_entry"] = map[string]interface{}{
			"id":       report.DataEntry.ID,
			"type":     report.DataEntry.Type,
			"title":    report.DataEntry.Title,
			"message":  report.DataEntry.Message,
			"entity":   report.DataEntry.Entity,
			"location": report.DataEntry.Location,
		}
	}

	if report.Metadata != nil {
		data["metadata"] = report.Metadata
	}

	return data
}

func (h *FileOutputHandler) Close() error {
	return nil
}

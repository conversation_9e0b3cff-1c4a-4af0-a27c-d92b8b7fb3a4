package output

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/general/pkg/agent"
)

type ConsoleOutputHandler struct {
	PrettyPrint bool
	ColorOutput bool
}

func NewConsoleOutputHandler() *ConsoleOutputHandler {
	return &ConsoleOutputHandler{}
}

func (h *ConsoleOutputHandler) Initialize(config map[string]interface{}) error {
	if pretty_print, ok := config["pretty_print"].(bool); ok {
		h.PrettyPrint = pretty_print
	} else {
		h.PrettyPrint = true
	}

	if color_output, ok := config["color_output"].(bool); ok {
		h.ColorOutput = color_output
	} else {
		h.ColorOutput = true
	}

	return nil
}

func (h *ConsoleOutputHandler) SaveReport(ctx context.Context, report agent.Report) error {
	if h.PrettyPrint {
		h.printPrettyReport(report)
	} else {
		data, err := json.Marshal(report)
		if err != nil {
			return fmt.Errorf("failed to marshal report: %w", err)
		}
		fmt.Println(string(data))
	}

	return nil
}

func (h *ConsoleOutputHandler) printPrettyReport(report agent.Report) {
	fmt.Printf("\n=== Analysis Report ===\n")
	fmt.Printf("ID: %s\n", report.DataEntry.ID)
	fmt.Printf("Title: %s\n", report.DataEntry.Title)
	fmt.Printf("Timestamp: %s\n", report.Timestamp)
	fmt.Printf("\n--- Analysis ---\n")
	fmt.Printf("Error Type: %s\n", report.Analysis.ErrorType)
	fmt.Printf("Severity: %s\n", report.Analysis.Severity)
	fmt.Printf("Category: %s\n", report.Analysis.Category)
	fmt.Printf("Description: %s\n", report.Analysis.Description)
	fmt.Printf("Root Cause: %s\n", report.Analysis.RootCause)
	fmt.Printf("Business Impact: %s\n", report.Analysis.BusinessImpact)
	fmt.Printf("Suggested Action: %s\n", report.Analysis.SuggestedAction)
	fmt.Printf("Is Retryable: %t\n", report.Analysis.IsRetryable)
	fmt.Printf("Compliance Risk: %s\n", report.Analysis.ComplianceRisk)
	fmt.Printf("Customer Impact: %s\n", report.Analysis.CustomerImpact)
	fmt.Printf("======================\n\n")
}

func (h *ConsoleOutputHandler) Close() error {
	return nil
}

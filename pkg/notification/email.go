package notification

import (
	"context"

	"github.com/general/pkg/agent"
)

type EmailNotificationHandler struct {
	Enabled    bool
	SmtpServer string
	SmtpPort   int
	Username   string
	Password   string
	FromEmail  string
	ToEmails   []string
	// TODO: Add SMTP client
}

func NewEmailNotificationHandler() *EmailNotificationHandler {
	return &EmailNotificationHandler{}
}

func (h *EmailNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.Enabled = enabled
	}

	if smtpServer, ok := config["smtp_server"].(string); ok {
		h.SmtpServer = smtpServer
	}

	if smtpPort, ok := config["smtp_port"].(int); ok {
		h.SmtpPort = smtpPort
	} else {
		h.SmtpPort = 587 // default SMTP port
	}

	if username, ok := config["username"].(string); ok {
		h.Username = username
	}

	if password, ok := config["password"].(string); ok {
		h.Password = password
	}

	if fromEmail, ok := config["from_email"].(string); ok {
		h.FromEmail = fromEmail
	}

	if toEmails, ok := config["to_emails"].([]interface{}); ok {
		h.ToEmails = make([]string, len(toEmails))
		for i, email := range toEmails {
			if str, ok := email.(string); ok {
				h.ToEmails[i] = str
			}
		}
	} else if toEmails, ok := config["to_emails"].([]string); ok {
		h.ToEmails = toEmails
	}

	if !h.Enabled || h.SmtpServer == "" || len(h.ToEmails) == 0 {
		h.Enabled = false
		return nil
	}

	// TODO: Initialize SMTP client

	return nil
}

func (h *EmailNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	if !h.Enabled {
		return nil
	}

	// TODO: send email

	return nil
}

func (h *EmailNotificationHandler) Close() error {
	return nil
}

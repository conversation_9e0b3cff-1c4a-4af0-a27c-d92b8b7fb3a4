package notification

import (
	"context"
	"errors"

	"github.com/general/pkg/agent"
	"github.com/go-resty/resty/v2"
)

type OneSignalNotificationHandler struct {
	Client                *resty.Client
	Enabled               bool
	OneSignalAPPID        string
	OneSignalAPIKey       string
	PushNotificationURL   string
	TargetSubscriptionIDs []string
	Heading               string

	// TODO: Add more infor for onesignal
}

type NotificationRequestForOneSignal struct {
	AppID                  string            `json:"app_id"`
	IncludeSubscriptionIDS []string          `json:"include_subscription_ids"`
	Headings               map[string]string `json:"headings"`
	Contents               map[string]string `json:"contents"`
}

func NewOneSignalNotificationHandler() *OneSignalNotificationHandler {
	return &OneSignalNotificationHandler{}
}

func (h *OneSignalNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.Enabled = enabled
	}

	if onesignal_app_id, ok := config["onesignal_app_id"].(string); ok {
		h.OneSignalAPPID = onesignal_app_id
	}

	if onesignal_api_key, ok := config["onesignal_api_key"].(string); ok {
		h.OneSignalAPIKey = onesignal_api_key
	}

	if push_notification_url, ok := config["push_notification_url"].(string); ok {
		h.PushNotificationURL = push_notification_url
	} else {
		h.PushNotificationURL = "https://onesignal.com/api/v1/notifications"
	}

	if !h.Enabled {
		return errors.New("onesignal notification is not enabled")
	}

	if h.OneSignalAPPID == "" {
		return errors.New("onesignal app id is not set")
	}

	if h.OneSignalAPIKey == "" {
		return errors.New("onesignal api key is not set")
	}

	// TODO: Initialize SMTP client

	return nil
}

func (h *OneSignalNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {

	request := NotificationRequestForOneSignal{
		AppID:                  h.OneSignalAPPID,
		IncludeSubscriptionIDS: h.TargetSubscriptionIDs,
		Headings: map[string]string{
			"en": h.Heading,
		},
		Contents: map[string]string{
			"en": report.Analysis.ID,
		},
	}

	req := h.Client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(request)

	if h.OneSignalAPIKey != "" {
		req.SetHeader("Authorization", "Basic "+h.OneSignalAPIKey)
	}

	resp, err := req.Post(h.PushNotificationURL)
	if err != nil {
		return err
	}

	if resp.StatusCode() != 200 {
		return errors.New("onesignal notification failed")
	}

	return nil
}

func (h *OneSignalNotificationHandler) Close() error {
	return nil
}

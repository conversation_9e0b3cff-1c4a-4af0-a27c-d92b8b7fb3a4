package notification

import (
	"context"

	"github.com/general/pkg/agent"
)

type SlackNotificationHandler struct {
	Enabled    bool
	WebhookURL string
	Channel    string
	Username   string
	// TODO: Add Slack client
}

func NewSlackNotificationHandler() *SlackNotificationHandler {
	return &SlackNotificationHandler{}
}

func (h *SlackNotificationHandler) Initialize(config map[string]interface{}) error {
	if enabled, ok := config["enabled"].(bool); ok {
		h.Enabled = enabled
	}

	if webhook_url, ok := config["webhook_url"].(string); ok {
		h.WebhookURL = webhook_url
	}

	if channel, ok := config["channel"].(string); ok {
		h.Channel = channel
	}

	if username, ok := config["username"].(string); ok {
		h.Username = username
	} else {
		h.Username = "AI Agent"
	}

	if !h.Enabled || h.WebhookURL == "" {
		h.Enabled = false
		return nil
	}

	// TODO: Initialize Slack client

	return nil
}

func (h *SlackNotificationHandler) SendNotification(ctx context.Context, report agent.Report) error {
	if !h.Enabled {
		return nil
	}

	// TODO: send notification to Slack

	return nil
}

func (h *SlackNotificationHandler) Close() error {
	return nil
}

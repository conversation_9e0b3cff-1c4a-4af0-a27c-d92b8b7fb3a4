package factory

import (
	"fmt"

	"github.com/general/pkg/agent"
	"github.com/general/pkg/datasource"
	"github.com/general/pkg/notification"
	"github.com/general/pkg/output"
	"github.com/general/pkg/prompt"
	"github.com/general/pkg/provider"
)

type ComponentFactory struct{}

func NewComponentFactory() *ComponentFactory {
	return &ComponentFactory{}
}

func (f *ComponentFactory) CreateDataSource(dsType string) (agent.DataSource, error) {
	// can be more, don't worry
	switch dsType {
	case "database":
		return datasource.NewDatabaseDataSource(), nil
	default:
		return nil, fmt.Errorf("unsupported data source type: %s", dsType)
	}
}

func (f *ComponentFactory) CreateAIProvider(providerType string) (agent.AIProvider, error) {
	switch providerType {
	case "openai_compatible":
		return provider.NewOpenAICompatibleProvider(), nil
	case "other":
		return provider.NewOtherProvider(), nil
	default:
		return nil, fmt.Errorf("unsupported AI provider type: %s", providerType)
	}
}

func (f *ComponentFactory) CreateOutputHandler(handlerType string) (agent.OutputHandler, error) {
	switch handlerType {
	case "file":
		return output.NewFileOutputHandler(), nil
	case "database":
		return output.NewDatabaseOutputHandler(), nil
	case "api":
		return output.NewAPIOutputHandler(), nil
	case "console":
		return output.NewConsoleOutputHandler(), nil
	default:
		return nil, fmt.Errorf("unsupported output handler type: %s", handlerType)
	}
}

func (f *ComponentFactory) CreateNotificationHandler(handlerType string) (agent.NotificationHandler, error) {
	switch handlerType {
	case "slack":
		return notification.NewSlackNotificationHandler(), nil
	case "email":
		return notification.NewEmailNotificationHandler(), nil
	case "onesignal":
		return notification.NewOneSignalNotificationHandler(), nil
	default:
		return nil, fmt.Errorf("unsupported notification handler type: %s", handlerType)
	}
}

func (f *ComponentFactory) CreatePromptBuilder(domain string) agent.PromptBuilder {
	return prompt.NewTemplateBuilder(domain)
}

// AgentBuilder helps build a complete agent with all components
type AgentBuilder struct {
	factory *ComponentFactory
	agent   *agent.BaseAgent
}

// NewAgentBuilder creates a new agent builder
func NewAgentBuilder() *AgentBuilder {
	return &AgentBuilder{
		factory: NewComponentFactory(),
		agent:   agent.NewBaseAgent(),
	}
}

// WithConfig initializes the agent with configuration
func (b *AgentBuilder) WithConfig(configPath string) *AgentBuilder {
	if err := b.agent.Initialize(configPath); err != nil {
		panic(fmt.Sprintf("failed to initialize agent: %v", err))
	}
	return b
}

// WithDataSource sets up the data source
func (b *AgentBuilder) WithDataSource(dsType string) *AgentBuilder {
	ds, err := b.factory.CreateDataSource(dsType)
	if err != nil {
		panic(fmt.Sprintf("failed to create data source: %v", err))
	}

	if err := b.agent.SetDataSource(ds); err != nil {
		panic(fmt.Sprintf("failed to set data source: %v", err))
	}

	return b
}

// WithAIProvider sets up the AI provider
func (b *AgentBuilder) WithAIProvider(providerType string) *AgentBuilder {
	provider, err := b.factory.CreateAIProvider(providerType)
	if err != nil {
		panic(fmt.Sprintf("failed to create AI provider: %v", err))
	}

	if err := b.agent.SetAIProvider(provider); err != nil {
		panic(fmt.Sprintf("failed to set AI provider: %v", err))
	}

	return b
}

// WithOutputHandler sets up the output handler
func (b *AgentBuilder) WithOutputHandler(handlerType string) *AgentBuilder {
	handler, err := b.factory.CreateOutputHandler(handlerType)
	if err != nil {
		panic(fmt.Sprintf("failed to create output handler: %v", err))
	}

	if err := b.agent.SetOutputHandler(handler); err != nil {
		panic(fmt.Sprintf("failed to set output handler: %v", err))
	}

	return b
}

// WithNotificationHandler sets up the notification handler
func (b *AgentBuilder) WithNotificationHandler(handlerType string) *AgentBuilder {
	handler, err := b.factory.CreateNotificationHandler(handlerType)
	if err != nil {
		panic(fmt.Sprintf("failed to create notification handler: %v", err))
	}

	if err := b.agent.SetNotificationHandler(handler); err != nil {
		panic(fmt.Sprintf("failed to set notification handler: %v", err))
	}

	return b
}

// WithPromptBuilder sets up the prompt builder
func (b *AgentBuilder) WithPromptBuilder(domain string) *AgentBuilder {
	builder := b.factory.CreatePromptBuilder(domain)
	b.agent.SetPromptBuilder(builder)
	return b
}

// WithPlugin adds a plugin to the agent
func (b *AgentBuilder) WithPlugin(plugin agent.Plugin) *AgentBuilder {
	if err := b.agent.AddPlugin(plugin); err != nil {
		panic(fmt.Sprintf("failed to add plugin: %v", err))
	}
	return b
}

// Build returns the configured agent
func (b *AgentBuilder) Build() agent.Agent {
	return b.agent
}

// BuildDefault creates an agent with default components based on configuration
func (b *AgentBuilder) BuildDefault(configPath string) agent.Agent {
	return b.WithConfig(configPath).
		WithDataSource("json_file").
		WithAIProvider("openai_compatible").
		WithOutputHandler("file").
		WithNotificationHandler("console").
		WithPromptBuilder("general").
		Build()
}

package agent

import "context"

type DataEntry struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Title    string                 `json:"title,omitempty"`
	Message  string                 `json:"message,omitempty"`
	Entity   string                 `json:"entity,omitempty"`
	Location string                 `json:"location,omitempty"`
	Proto    string                 `json:"proto,omitempty"`
	Data     map[string]interface{} `json:"data,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

type Analysis struct {
	ID              string                 `json:"id"`
	ErrorType       string                 `json:"error_type,omitempty"`
	Severity        string                 `json:"severity"`
	Category        string                 `json:"category"`
	Description     string                 `json:"description"`
	RootCause       string                 `json:"root_cause,omitempty"`
	BusinessImpact  string                 `json:"business_impact,omitempty"`
	SuggestedAction string                 `json:"suggested_action,omitempty"`
	IsRetryable     bool                   `json:"is_retryable"`
	ComplianceRisk  string                 `json:"compliance_risk,omitempty"`
	CustomerImpact  string                 `json:"customer_impact,omitempty"`
	Confidence      float64                `json:"confidence,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	CustomFields    map[string]interface{} `json:"custom_fields,omitempty"`
}

type Report struct {
	Timestamp   string                 `json:"timestamp"`
	AgentInfo   map[string]string      `json:"agent_info"`
	DataEntry   DataEntry              `json:"data_entry"`
	Analysis    Analysis               `json:"analysis"`
	ProcessedBy string                 `json:"processed_by"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// DataSource interface for different data source implementations
type DataSource interface {
	Initialize(config map[string]interface{}) error
	// GetEntries retrieves data entries to be processed
	GetEntries(ctx context.Context) ([]DataEntry, error)

	// GetDetailedData retrieves detailed data for a specific entry
	GetDetailedData(ctx context.Context, entryID string) (map[string]interface{}, error)
	Close() error
}

type AIProvider interface {
	Initialize(config map[string]interface{}) error
	Analyze(ctx context.Context, prompt string, data map[string]interface{}) (*Analysis, error)
	GetCapabilities() map[string]interface{}
	Close() error
}

type NotificationHandler interface {
	Initialize(config map[string]interface{}) error
	SendNotification(ctx context.Context, report Report) error
	Close() error
}

type OutputHandler interface {
	Initialize(config map[string]interface{}) error
	SaveReport(ctx context.Context, report Report) error
	Close() error
}

// PromptBuilder interface for building AI prompts
type PromptBuilder interface {
	// BuildPrompt builds a prompt for AI analysis
	BuildPrompt(entry DataEntry, detailedData map[string]interface{}, config map[string]interface{}) string

	// GetTemplate returns the prompt template
	GetTemplate() string

	// SetTemplate sets the prompt template
	SetTemplate(template string)
}

// FailureDetector interface for detecting failures in data
type FailureDetector interface {
	// IsFailure determines if a data entry represents a failure
	IsFailure(entry DataEntry) bool

	// GetFailureReason returns the reason why an entry is considered a failure
	GetFailureReason(entry DataEntry) string

	// Configure configures the failure detector
	Configure(config map[string]interface{}) error
}

// Agent interface represents the main agent functionality
type Agent interface {
	// Initialize initializes the agent with configuration
	Initialize(configPath string) error

	// Start starts the agent processing
	Start(ctx context.Context) error

	// Stop stops the agent processing
	Stop() error

	// ProcessSingle processes a single data entry
	ProcessSingle(ctx context.Context, entry DataEntry) (*Report, error)

	// GetStatus returns the current status of the agent
	GetStatus() map[string]interface{}

	// GetConfig returns the agent configuration
	GetConfig() map[string]interface{}
}

// Plugin interface for extending agent functionality
type Plugin interface {
	// GetName returns the plugin name
	GetName() string

	// GetVersion returns the plugin version
	GetVersion() string

	// Initialize initializes the plugin
	Initialize(config map[string]interface{}) error

	// Execute executes the plugin functionality
	Execute(ctx context.Context, data map[string]interface{}) (map[string]interface{}, error)

	// Close closes the plugin
	Close() error
}

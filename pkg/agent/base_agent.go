package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/general/pkg/config"
	"github.com/sirupsen/logrus"
)

// BaseAgent provides a base implementation of the Agent interface
type BaseAgent struct {
	config              *config.Config
	logger              *logrus.Logger
	dataSource          DataSource
	aiProvider          AIProvider
	notificationHandler NotificationHandler
	outputHandler       OutputHandler
	promptBuilder       PromptBuilder
	failureDetector     FailureDetector
	plugins             map[string]Plugin

	// Runtime state
	isRunning bool
	stopChan  chan struct{}
	mu        sync.RWMutex
	stats     AgentStats
}

// AgentStats holds agent statistics
type AgentStats struct {
	StartTime          time.Time `json:"start_time"`
	ProcessedEntries   int64     `json:"processed_entries"`
	FailedEntries      int64     `json:"failed_entries"`
	SuccessfulAnalyses int64     `json:"successful_analyses"`
	Errors             int64     `json:"errors"`
	LastProcessTime    time.Time `json:"last_process_time"`
}

// NewBaseAgent creates a new base agent instance
func NewBaseAgent() *BaseAgent {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &BaseAgent{
		logger:   logger,
		plugins:  make(map[string]Plugin),
		stopChan: make(chan struct{}),
		stats:    AgentStats{StartTime: time.Now()},
	}
}

// Initialize initializes the agent with configuration
func (a *BaseAgent) Initialize(configPath string) error {
	// Load configuration
	cfg, err := config.InitConfig(configPath)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	a.config = cfg
	a.logger.Infof("Initialized agent: %s v%s", cfg.Agent.Name, cfg.Agent.Version)

	return nil
}

// SetDataSource sets the data source implementation
func (a *BaseAgent) SetDataSource(ds DataSource) error {
	if a.config == nil {
		return fmt.Errorf("agent not initialized")
	}

	primaryDS := a.config.GetPrimaryDataSource()
	if primaryDS == nil {
		return fmt.Errorf("no primary data source configured")
	}

	// Convert config to map for interface compatibility
	dsConfig := map[string]interface{}{
		"type":               primaryDS.Type,
		"path":               primaryDS.Path,
		"format":             primaryDS.Format,
		"detailed_logs_path": primaryDS.DetailedLogsPath,
		"connection_string":  primaryDS.ConnectionString,
		"query":              primaryDS.Query,
	}

	if err := ds.Initialize(dsConfig); err != nil {
		return fmt.Errorf("failed to initialize data source: %w", err)
	}

	a.dataSource = ds
	return nil
}

// SetAIProvider sets the AI provider implementation
func (a *BaseAgent) SetAIProvider(ai AIProvider) error {
	if a.config == nil {
		return fmt.Errorf("agent not initialized")
	}

	// Convert config to map for interface compatibility
	aiConfig := map[string]interface{}{
		"type":            a.config.AIProvider.Type,
		"api_url":         a.config.AIProvider.APIURL,
		"model_name":      a.config.AIProvider.ModelName,
		"temperature":     a.config.AIProvider.Temperature,
		"max_tokens":      a.config.AIProvider.MaxTokens,
		"timeout_seconds": a.config.AIProvider.TimeoutSeconds,
		"api_key":         a.config.AIProvider.APIKey,
	}

	if err := ai.Initialize(aiConfig); err != nil {
		return fmt.Errorf("failed to initialize AI provider: %w", err)
	}

	a.aiProvider = ai
	return nil
}

// SetNotificationHandler sets the notification handler implementation
func (a *BaseAgent) SetNotificationHandler(nh NotificationHandler) error {
	if a.config == nil {
		return fmt.Errorf("agent not initialized")
	}

	// Convert config to map for interface compatibility
	notifConfig := map[string]interface{}{
		"enabled":  a.config.Notifications.Enabled,
		"channels": a.config.Notifications.Channels,
	}

	if err := nh.Initialize(notifConfig); err != nil {
		return fmt.Errorf("failed to initialize notification handler: %w", err)
	}

	a.notificationHandler = nh
	return nil
}

// SetOutputHandler sets the output handler implementation
func (a *BaseAgent) SetOutputHandler(oh OutputHandler) error {
	if a.config == nil {
		return fmt.Errorf("agent not initialized")
	}

	// Convert config to map for interface compatibility
	outputConfig := map[string]interface{}{
		"reports_directory": a.config.Output.ReportsDirectory,
		"filename_pattern":  a.config.Output.FilenamePattern,
		"include_raw_data":  a.config.Output.IncludeRawData,
		"pretty_print":      a.config.Output.PrettyPrint,
	}

	if err := oh.Initialize(outputConfig); err != nil {
		return fmt.Errorf("failed to initialize output handler: %w", err)
	}

	a.outputHandler = oh
	return nil
}

// SetPromptBuilder sets the prompt builder implementation
func (a *BaseAgent) SetPromptBuilder(pb PromptBuilder) {
	a.promptBuilder = pb
}

// AddPlugin adds a plugin to the agent
func (a *BaseAgent) AddPlugin(plugin Plugin) error {
	name := plugin.GetName()
	if _, exists := a.plugins[name]; exists {
		return fmt.Errorf("plugin %s already exists", name)
	}

	a.plugins[name] = plugin
	a.logger.Infof("Added plugin: %s v%s", name, plugin.GetVersion())
	return nil
}

// GetPlugin retrieves a plugin by name
func (a *BaseAgent) GetPlugin(name string) (Plugin, bool) {
	plugin, exists := a.plugins[name]
	return plugin, exists
}

// Start starts the agent processing
func (a *BaseAgent) Start(ctx context.Context) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if a.isRunning {
		return fmt.Errorf("agent is already running")
	}

	// Validate required components
	if err := a.validateComponents(); err != nil {
		return fmt.Errorf("component validation failed: %w", err)
	}

	a.isRunning = true
	a.stats.StartTime = time.Now()

	a.logger.Infof("🤖 Starting agent: %s", a.config.Agent.Name)

	// Start processing loop
	go a.processingLoop(ctx)

	return nil
}

// validateComponents validates that all required components are set
func (a *BaseAgent) validateComponents() error {
	if a.dataSource == nil {
		return fmt.Errorf("data source not set")
	}
	if a.aiProvider == nil {
		return fmt.Errorf("AI provider not set")
	}
	if a.outputHandler == nil {
		return fmt.Errorf("output handler not set")
	}
	if a.promptBuilder == nil {
		return fmt.Errorf("prompt builder not set")
	}
	if a.failureDetector == nil {
		return fmt.Errorf("failure detector not set")
	}
	return nil
}

// processingLoop runs the main processing loop
func (a *BaseAgent) processingLoop(ctx context.Context) {
	ticker := time.NewTicker(time.Duration(a.config.Processing.CheckInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			a.logger.Info("Processing loop stopped due to context cancellation")
			return
		case <-a.stopChan:
			a.logger.Info("Processing loop stopped")
			return
		case <-ticker.C:
			if err := a.processEntries(ctx); err != nil {
				a.logger.Errorf("Error processing entries: %v", err)
				a.stats.Errors++
			}
		}
	}
}

// processEntries processes all available entries
func (a *BaseAgent) processEntries(ctx context.Context) error {
	entries, err := a.dataSource.GetEntries(ctx)
	if err != nil {
		return fmt.Errorf("failed to get entries: %w", err)
	}

	if len(entries) == 0 {
		return nil
	}

	a.logger.Infof("🔍 Processing %d entries...", len(entries))

	failureCount := 0
	for _, entry := range entries {
		if a.failureDetector.IsFailure(entry) {
			failureCount++
			if _, err := a.ProcessSingle(ctx, entry); err != nil {
				a.logger.Errorf("Failed to process entry %s: %v", entry.ID, err)
				a.stats.FailedEntries++
			} else {
				a.stats.SuccessfulAnalyses++
			}
		}
		a.stats.ProcessedEntries++
	}

	a.stats.LastProcessTime = time.Now()
	a.logger.Infof("🎯 Processed %d failures out of %d total entries", failureCount, len(entries))

	return nil
}

// ProcessSingle processes a single data entry
func (a *BaseAgent) ProcessSingle(ctx context.Context, entry DataEntry) (*Report, error) {
	a.logger.Infof("⚠️  Processing entry: %s - %s", entry.ID, entry.Title)

	// Get detailed data
	detailedData, err := a.dataSource.GetDetailedData(ctx, entry.ID)
	if err != nil {
		a.logger.Warnf("Failed to get detailed data for %s: %v", entry.ID, err)
		detailedData = make(map[string]interface{})
	}

	// Build prompt
	prompt := a.promptBuilder.BuildPrompt(entry, detailedData, map[string]interface{}{
		"domain":     a.config.Analysis.Domain,
		"categories": a.config.Analysis.Categories,
	})

	// Analyze with AI
	analysis, err := a.aiProvider.Analyze(ctx, prompt, detailedData)
	if err != nil {
		return nil, fmt.Errorf("AI analysis failed: %w", err)
	}

	// Create report
	report := &Report{
		Timestamp: time.Now().Format(time.RFC3339),
		AgentInfo: map[string]string{
			"name":    a.config.Agent.Name,
			"version": a.config.Agent.Version,
			"type":    a.config.Agent.Type,
		},
		DataEntry:   entry,
		Analysis:    *analysis,
		ProcessedBy: fmt.Sprintf("%s v%s", a.config.Agent.Name, a.config.Agent.Version),
	}

	// Save report
	if err := a.outputHandler.SaveReport(ctx, *report); err != nil {
		a.logger.Errorf("Failed to save report for %s: %v", entry.ID, err)
	}

	// Send notification if enabled and handler is set
	if a.config.Notifications.Enabled && a.notificationHandler != nil {
		if err := a.notificationHandler.SendNotification(ctx, *report); err != nil {
			a.logger.Warnf("Failed to send notification for %s: %v", entry.ID, err)
		}
	}

	a.logger.Infof("✅ Completed analysis for %s", entry.ID)
	return report, nil
}

// Stop stops the agent processing
func (a *BaseAgent) Stop() error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if !a.isRunning {
		return fmt.Errorf("agent is not running")
	}

	close(a.stopChan)
	a.isRunning = false

	// Close all components
	if a.dataSource != nil {
		a.dataSource.Close()
	}
	if a.aiProvider != nil {
		a.aiProvider.Close()
	}
	if a.notificationHandler != nil {
		a.notificationHandler.Close()
	}
	if a.outputHandler != nil {
		a.outputHandler.Close()
	}

	// Close plugins
	for name, plugin := range a.plugins {
		if err := plugin.Close(); err != nil {
			a.logger.Warnf("Failed to close plugin %s: %v", name, err)
		}
	}

	a.logger.Info("🎉 Agent stopped successfully!")
	return nil
}

// GetStatus returns the current status of the agent
func (a *BaseAgent) GetStatus() map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()

	status := map[string]interface{}{
		"running": a.isRunning,
		"stats":   a.stats,
		"config":  a.config.Agent,
		"components": map[string]bool{
			"data_source":          a.dataSource != nil,
			"ai_provider":          a.aiProvider != nil,
			"notification_handler": a.notificationHandler != nil,
			"output_handler":       a.outputHandler != nil,
			"prompt_builder":       a.promptBuilder != nil,
			"failure_detector":     a.failureDetector != nil,
		},
		"plugins": make(map[string]string),
	}

	// Add plugin info
	plugins := status["plugins"].(map[string]string)
	for name, plugin := range a.plugins {
		plugins[name] = plugin.GetVersion()
	}

	return status
}

// GetConfig returns the agent configuration
func (a *BaseAgent) GetConfig() map[string]interface{} {
	if a.config == nil {
		return nil
	}

	return map[string]interface{}{
		"agent":         a.config.Agent,
		"data_sources":  a.config.DataSources,
		"ai_provider":   a.config.AIProvider,
		"processing":    a.config.Processing,
		"analysis":      a.config.Analysis,
		"notifications": a.config.Notifications,
		"output":        a.config.Output,
	}
}

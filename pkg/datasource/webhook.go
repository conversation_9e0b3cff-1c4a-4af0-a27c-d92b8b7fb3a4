package datasource

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/general/pkg/agent"
	"github.com/sirupsen/logrus"
)

// WebhookDataSource implements DataSource for webhook-based real-time data
type WebhookDataSource struct {
	enabled      bool
	port         int
	endpoint     string
	server       *http.Server
	entryQueue   chan agent.DataEntry
	detailedData map[string]map[string]interface{}
	mu           sync.RWMutex
	logger       *logrus.Logger
}

// NewWebhookDataSource creates a new webhook data source
func NewWebhookDataSource() *WebhookDataSource {
	logger := logrus.New()
	return &WebhookDataSource{
		entryQueue:   make(chan agent.DataEntry, 1000), // Buffer for 1000 entries
		detailedData: make(map[string]map[string]interface{}),
		logger:       logger,
	}
}

// Initialize initializes the webhook data source
func (ds *WebhookDataSource) Initialize(config map[string]interface{}) error {
	// Extract configuration
	if enabled, ok := config["enabled"].(bool); ok {
		ds.enabled = enabled
	} else {
		ds.enabled = true
	}

	if !ds.enabled {
		return nil
	}

	if port, ok := config["port"].(int); ok {
		ds.port = port
	} else {
		ds.port = 8080 // default port
	}

	if endpoint, ok := config["endpoint"].(string); ok {
		ds.endpoint = endpoint
	} else {
		ds.endpoint = "/webhook" // default endpoint
	}

	// Setup HTTP server
	mux := http.NewServeMux()
	mux.HandleFunc(ds.endpoint, ds.handleWebhook)
	mux.HandleFunc("/health", ds.handleHealth)

	ds.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", ds.port),
		Handler: mux,
	}

	// Start server in goroutine
	go func() {
		ds.logger.Infof("🌐 Webhook server starting on port %d, endpoint %s", ds.port, ds.endpoint)
		if err := ds.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ds.logger.Errorf("Webhook server error: %v", err)
		}
	}()

	return nil
}

// handleWebhook handles incoming webhook requests
func (ds *WebhookDataSource) handleWebhook(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		ds.logger.Errorf("Failed to read webhook body: %v", err)
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// Parse JSON
	var webhookData map[string]interface{}
	if err := json.Unmarshal(body, &webhookData); err != nil {
		ds.logger.Errorf("Failed to parse webhook JSON: %v", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Convert to DataEntry
	entry := ds.convertToDataEntry(webhookData, r.Header)

	// Store detailed data
	ds.mu.Lock()
	ds.detailedData[entry.ID] = webhookData
	ds.mu.Unlock()

	// Add to queue (non-blocking)
	select {
	case ds.entryQueue <- entry:
		ds.logger.Infof("📨 Webhook data received: %s", entry.ID)
	default:
		ds.logger.Warnf("⚠️ Webhook queue full, dropping entry: %s", entry.ID)
	}

	// Respond with success
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":   "success",
		"message":  "Webhook received",
		"entry_id": entry.ID,
	})
}

func (ds *WebhookDataSource) convertToDataEntry(data map[string]interface{}, headers http.Header) agent.DataEntry {
	// Generate unique ID
	entryID := fmt.Sprintf("webhook_%d", time.Now().UnixNano())

	// Extract common fields with fallbacks
	entry := agent.DataEntry{
		ID:       entryID,
		Type:     ds.getStringValue(data, "type", "webhook"),
		Title:    ds.getStringValue(data, "title", "Webhook Event"),
		Message:  ds.getStringValue(data, "message", ""),
		Entity:   ds.getStringValue(data, "entity", "webhook"),
		Location: ds.getStringValue(data, "location", ""),
		Proto:    ds.getStringValue(data, "proto", "HTTP"),
		Data:     data,
		Metadata: map[string]interface{}{
			"timestamp":    time.Now().Format(time.RFC3339),
			"source":       "webhook",
			"content_type": headers.Get("Content-Type"),
			"user_agent":   headers.Get("User-Agent"),
			"remote_addr":  headers.Get("X-Forwarded-For"),
		},
	}

	// If no message, try to extract from common fields
	if entry.Message == "" {
		if desc, ok := data["description"].(string); ok {
			entry.Message = desc
		} else if event, ok := data["event"].(string); ok {
			entry.Message = event
		} else if text, ok := data["text"].(string); ok {
			entry.Message = text
		} else {
			entry.Message = "Webhook event received"
		}
	}

	return entry
}

// getStringValue safely extracts string value with fallback
func (ds *WebhookDataSource) getStringValue(data map[string]interface{}, key, fallback string) string {
	if val, ok := data[key].(string); ok {
		return val
	}
	return fallback
}

// handleHealth handles health check requests
func (ds *WebhookDataSource) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":     "healthy",
		"queue_size": len(ds.entryQueue),
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

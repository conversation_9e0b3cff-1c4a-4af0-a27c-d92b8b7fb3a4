package config

import (
	"encoding/json"
	"fmt"
	"os"
)

// AgentInfo contains basic agent information
type AgentInfo struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// DataSource represents a data source configuration
type DataSource struct {
	Type             string `json:"type"`
	Path             string `json:"path"`
	Format           string `json:"format"`
	DetailedLogsPath string `json:"detailed_logs_path"`
	ConnectionString string `json:"connection_string"`
	Query            string `json:"query"`
}

// AIProvider represents AI service configuration
type AIProvider struct {
	Type           string  `json:"type"`
	APIURL         string  `json:"api_url"`
	ModelName      string  `json:"model_name"`
	Temperature    float64 `json:"temperature"`
	MaxTokens      int     `json:"max_tokens"`
	TimeoutSeconds int     `json:"timeout_seconds"`
	APIKey         string  `json:"api_key"`
}

// Analysis represents analysis configuration
type Analysis struct {
	Domain            string   `json:"domain"`
	PromptTemplate    string   `json:"prompt_template"`
	SeverityThreshold string   `json:"severity_threshold"`
	Categories        []string `json:"categories"`
	OutputFormat      string   `json:"output_format"`
}

// NotificationChannel represents a notification channel
type NotificationChannel struct {
	Enabled    bool   `json:"enabled"`
	URL        string `json:"url"`
	WebhookURL string `json:"webhook_url"`
}

type FailureDetection struct {
	Enabled  bool     `json:"enabled"`
	Keywords []string `json:"keywords"`
	LogTypes []string `json:"log_types"`
}

type Processing struct {
	CheckInterval      int              `json:"check_interval"`
	MaxRetries         int              `json:"max_retries"`
	BatchSize          int              `json:"batch_size"`
	ParallelProcessing bool             `json:"parallel_processing"`
	FailureDetection   FailureDetection `json:"failure_detection"`
}

// Notifications represents notification configuration
type Notifications struct {
	Enabled  bool                           `json:"enabled"`
	Channels map[string]NotificationChannel `json:"channels"`
}

// Output represents output configuration
type Output struct {
	ReportsDirectory string `json:"reports_directory"`
	FilenamePattern  string `json:"filename_pattern"`
	IncludeRawData   bool   `json:"include_raw_data"`
	PrettyPrint      bool   `json:"pretty_print"`
}

// Config represents the complete agent configuration
type Config struct {
	Agent         AgentInfo             `json:"agent"`
	DataSources   map[string]DataSource `json:"data_sources"`
	AIProvider    AIProvider            `json:"ai_provider"`
	Processing    Processing            `json:"processing"`
	Analysis      Analysis              `json:"analysis"`
	Notifications Notifications         `json:"notifications"`
	Output        Output                `json:"output"`
}

func InitConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse config: %w", err)
	}

	config.setDefaults()

	if err := config.validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &config, nil
}

func (c *Config) setDefaults() {
	if c.Agent.Type == "" {
		c.Agent.Type = "general"
	}

	if c.AIProvider.TimeoutSeconds == 0 {
		c.AIProvider.TimeoutSeconds = 60
	}

	if c.Analysis.OutputFormat == "" {
		c.Analysis.OutputFormat = "json"
	}

	if c.Output.ReportsDirectory == "" {
		c.Output.ReportsDirectory = "reports/"
	}

	if c.Output.FilenamePattern == "" {
		c.Output.FilenamePattern = "analysis_{id}_{timestamp}.json"
	}
}

func (c *Config) validate() error {
	if c.Agent.Name == "" {
		return fmt.Errorf("agent name is required")
	}

	if c.AIProvider.APIURL == "" {
		return fmt.Errorf("AI provider API URL is required")
	}

	if c.AIProvider.ModelName == "" {
		return fmt.Errorf("AI provider model name is required")
	}

	if len(c.DataSources) == 0 {
		return fmt.Errorf("at least one data source is required")
	}

	return nil
}

// GetPrimaryDataSource returns the primary data source
func (c *Config) GetPrimaryDataSource() *DataSource {
	if ds, exists := c.DataSources["primary"]; exists {
		return &ds
	}

	// Return first data source if primary doesn't exist
	for _, ds := range c.DataSources {
		return &ds
	}

	return nil
}
